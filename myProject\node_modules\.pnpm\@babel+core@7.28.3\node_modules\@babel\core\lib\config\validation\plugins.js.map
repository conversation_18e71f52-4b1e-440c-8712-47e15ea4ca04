{"version": 3, "names": ["_optionAssertions", "require", "VALIDATORS", "name", "assertString", "manipulateOptions", "assertFunction", "pre", "post", "inherits", "visitor", "assertVisitorMap", "parserOverride", "generatorOverride", "loc", "value", "obj", "assertObject", "Object", "keys", "for<PERSON>ach", "prop", "assertVisitorHandler", "enter", "exit", "Error", "msg", "key", "handler", "validatePluginObject", "rootPath", "type", "source", "validator", "optLoc", "parent", "invalidPluginPropertyError", "code"], "sources": ["../../../src/config/validation/plugins.ts"], "sourcesContent": ["import {\n  assertString,\n  assertFunction,\n  assertObject,\n  msg,\n} from \"./option-assertions.ts\";\n\nimport type {\n  ValidatorSet,\n  Validator,\n  OptionPath,\n  RootPath,\n} from \"./option-assertions.ts\";\nimport type { parse, ParserOptions } from \"@babel/parser\";\nimport type { Visitor } from \"@babel/traverse\";\nimport type { ValidatedOptions } from \"./options.ts\";\nimport type { File, PluginAPI, PluginPass } from \"../../index.ts\";\n\n// Note: The casts here are just meant to be static assertions to make sure\n// that the assertion functions actually assert that the value's type matches\n// the declared types.\nconst VALIDATORS: ValidatorSet = {\n  name: assertString as Validator<PluginObject[\"name\"]>,\n  manipulateOptions: assertFunction as Validator<\n    PluginObject[\"manipulateOptions\"]\n  >,\n  pre: assertFunction as Validator<PluginObject[\"pre\"]>,\n  post: assertFunction as Validator<PluginObject[\"post\"]>,\n  inherits: assertFunction as Valida<PERSON><PluginObject[\"inherits\"]>,\n  visitor: assertVisitorMap as Validator<PluginObject[\"visitor\"]>,\n\n  parserOverride: assertFunction as Validator<PluginObject[\"parserOverride\"]>,\n  generatorOverride: assertFunction as Validator<\n    PluginObject[\"generatorOverride\"]\n  >,\n};\n\nfunction assertVisitorMap(loc: OptionPath, value: unknown): Visitor {\n  const obj = assertObject(loc, value);\n  if (obj) {\n    Object.keys(obj).forEach(prop => {\n      if (prop !== \"_exploded\" && prop !== \"_verified\") {\n        assertVisitorHandler(prop, obj[prop]);\n      }\n    });\n\n    if (obj.enter || obj.exit) {\n      throw new Error(\n        `${msg(\n          loc,\n        )} cannot contain catch-all \"enter\" or \"exit\" handlers. Please target individual nodes.`,\n      );\n    }\n  }\n  return obj as Visitor;\n}\n\nfunction assertVisitorHandler(\n  key: string,\n  value: unknown,\n): asserts value is VisitorHandler {\n  if (value && typeof value === \"object\") {\n    Object.keys(value).forEach((handler: string) => {\n      if (handler !== \"enter\" && handler !== \"exit\") {\n        throw new Error(\n          `.visitor[\"${key}\"] may only have .enter and/or .exit handlers.`,\n        );\n      }\n    });\n  } else if (typeof value !== \"function\") {\n    throw new Error(`.visitor[\"${key}\"] must be a function`);\n  }\n}\n\ntype VisitorHandler =\n  | Function\n  | {\n      enter?: Function;\n      exit?: Function;\n    };\n\nexport type PluginObject<S extends PluginPass = PluginPass> = {\n  name?: string;\n  manipulateOptions?: (\n    options: ValidatedOptions,\n    parserOpts: ParserOptions,\n  ) => void;\n  pre?: (this: S, file: File) => void | Promise<void>;\n  post?: (this: S, file: File) => void | Promise<void>;\n  inherits?: (\n    api: PluginAPI,\n    options: unknown,\n    dirname: string,\n  ) => PluginObject;\n  visitor?: Visitor<S>;\n  parserOverride?: (\n    ...args: [...Parameters<typeof parse>, typeof parse]\n  ) => ReturnType<typeof parse>;\n  generatorOverride?: Function;\n};\n\nexport function validatePluginObject(obj: {\n  [key: string]: unknown;\n}): PluginObject {\n  const rootPath: RootPath = {\n    type: \"root\",\n    source: \"plugin\",\n  };\n  Object.keys(obj).forEach((key: string) => {\n    const validator = VALIDATORS[key];\n\n    if (validator) {\n      const optLoc: OptionPath = {\n        type: \"option\",\n        name: key,\n        parent: rootPath,\n      };\n      validator(optLoc, obj[key]);\n    } else {\n      const invalidPluginPropertyError = new Error(\n        `.${key} is not a valid Plugin property`,\n      );\n      // @ts-expect-error todo(flow->ts) consider adding BabelConfigError with code field\n      invalidPluginPropertyError.code = \"BABEL_UNKNOWN_PLUGIN_PROPERTY\";\n      throw invalidPluginPropertyError;\n    }\n  });\n\n  return obj as any;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,iBAAA,GAAAC,OAAA;AAqBA,MAAMC,UAAwB,GAAG;EAC/BC,IAAI,EAAEC,8BAA+C;EACrDC,iBAAiB,EAAEC,gCAElB;EACDC,GAAG,EAAED,gCAAgD;EACrDE,IAAI,EAAEF,gCAAiD;EACvDG,QAAQ,EAAEH,gCAAqD;EAC/DI,OAAO,EAAEC,gBAAsD;EAE/DC,cAAc,EAAEN,gCAA2D;EAC3EO,iBAAiB,EAAEP;AAGrB,CAAC;AAED,SAASK,gBAAgBA,CAACG,GAAe,EAAEC,KAAc,EAAW;EAClE,MAAMC,GAAG,GAAG,IAAAC,8BAAY,EAACH,GAAG,EAAEC,KAAK,CAAC;EACpC,IAAIC,GAAG,EAAE;IACPE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;MAC/B,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,WAAW,EAAE;QAChDC,oBAAoB,CAACD,IAAI,EAAEL,GAAG,CAACK,IAAI,CAAC,CAAC;MACvC;IACF,CAAC,CAAC;IAEF,IAAIL,GAAG,CAACO,KAAK,IAAIP,GAAG,CAACQ,IAAI,EAAE;MACzB,MAAM,IAAIC,KAAK,CACb,GAAG,IAAAC,qBAAG,EACJZ,GACF,CAAC,uFACH,CAAC;IACH;EACF;EACA,OAAOE,GAAG;AACZ;AAEA,SAASM,oBAAoBA,CAC3BK,GAAW,EACXZ,KAAc,EACmB;EACjC,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACtCG,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAEQ,OAAe,IAAK;MAC9C,IAAIA,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,MAAM,EAAE;QAC7C,MAAM,IAAIH,KAAK,CACb,aAAaE,GAAG,gDAClB,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI,OAAOZ,KAAK,KAAK,UAAU,EAAE;IACtC,MAAM,IAAIU,KAAK,CAAC,aAAaE,GAAG,uBAAuB,CAAC;EAC1D;AACF;AA6BO,SAASE,oBAAoBA,CAACb,GAEpC,EAAgB;EACf,MAAMc,QAAkB,GAAG;IACzBC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE;EACV,CAAC;EACDd,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACI,OAAO,CAAEO,GAAW,IAAK;IACxC,MAAMM,SAAS,GAAG/B,UAAU,CAACyB,GAAG,CAAC;IAEjC,IAAIM,SAAS,EAAE;MACb,MAAMC,MAAkB,GAAG;QACzBH,IAAI,EAAE,QAAQ;QACd5B,IAAI,EAAEwB,GAAG;QACTQ,MAAM,EAAEL;MACV,CAAC;MACDG,SAAS,CAACC,MAAM,EAAElB,GAAG,CAACW,GAAG,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL,MAAMS,0BAA0B,GAAG,IAAIX,KAAK,CAC1C,IAAIE,GAAG,iCACT,CAAC;MAEDS,0BAA0B,CAACC,IAAI,GAAG,+BAA+B;MACjE,MAAMD,0BAA0B;IAClC;EACF,CAAC,CAAC;EAEF,OAAOpB,GAAG;AACZ;AAAC", "ignoreList": []}