#include "DBUtils";

(function () {
    var doc = getCurrentDocument();
    var sql = "SELECT * FROM tlk_GYPT_01_MAIN ";
    var flag = IsRole("系统管理员,总经理,供应链管理部负责人,综合部财务组,综合部数据组");
    var wherePart = "WHERE 1 = 1 ";
    var billNumber = doc.getItemValueAsString("编号");
    var pyBillNumber = doc.getItemValueAsString("普源采购订单");
    var cger = doc.getItemValueAsString("采购员");
    var purchaseBN = doc.getItemValueAsString("OA采购申请单");
    var orderBN = doc.getItemValueAsString("OA采购订单");
    var paymentBN = doc.getItemValueAsString("OA采购付款申请");
    var trackKind = doc.getItemValueAsString("跟踪类型");
    var sku = doc.getItemValueAsString("SKU");
    var stateLabel = doc.getItemValueAsString("审批状态");
    var delayDays = doc.getItemValueAsString("逾期天数");
    var isEnd = doc.getItemValueAsString("结束");
    if (isNotNull(billNumber))
        wherePart += " AND ITEM_编号 LIKE '%" + billNumber + "%' ";
    if (isNotNull(pyBillNumber))
        wherePart += " AND ITEM_普源采购订单编号 LIKE '%" + pyBillNumber + "%' ";
    if (isNotNull(cger))
        wherePart += " AND ITEM_采购员 LIKE '%" + cger + "%' ";
    if (isNotNull(purchaseBN))
        wherePart += " AND ITEM_采购申请单编号 LIKE '%" + purchaseBN + "%' ";
    if (isNotNull(orderBN))
        wherePart += " AND ITEM_采购订单编号 LIKE '%" + orderBN + "%' ";
    if (isNotNull(paymentBN))
        wherePart += " AND ITEM_采购付款单编号 LIKE '%" + paymentBN + "%' ";
    if (isNotNull(trackKind))
        wherePart += " AND ITEM_跟踪类型 LIKE '%" + trackKind + "%' ";
    if (isNotNull(sku))
        wherePart += " AND ITEM_SKU LIKE '%" + sku + "%' ";
    if (isNotNull(stateLabel))
        wherePart += " AND STATELABEL LIKE '%" + stateLabel + "%' ";
    if (isNotNull(delayDays))
        wherePart += " AND ITEM_本次逾期天数 >= " + delayDays + " ";
    if ("否".equals(isEnd))
        wherePart += " AND (STATELABEL <> '结束' AND STATELABEL <> '作废' AND AUDITORNAMES <> '') "
    sql += wherePart;
    debug("sql=" + sql);
    if (flag)
        return sql + " ORDER BY ITEM_跟踪类型, ITEM_跟踪类型 DESC ";
    else {
        var user = getWebUser();
        var flowId = "__AuEFr0wOVUvaNSk8YBI"; 
        var users = GetAgents(user, flowId);
        sql = UserSqlByUserField(sql, "采购员", users);
        return sql + " ORDER BY ITEM_跟踪类型, ITEM_编号 DESC ";
    }
})()


#include "DBUtils";

(function () {
  /* var sql = "SELECT * FROM tlk_gyfh_ft_01_freighttrial_by_month ";
  var flag = IsRole("系统管理员,总经理,供应链管理部负责人");
  if (flag == false){
    var flowId = "__eGfOEMOAWHCpZyRRC3v";
    var users = GetAgents(user, flowId);
    sql = UserSqlByUserField(sourceSql, "填写人", users);
  }
  sql += " ORDER BY ITEM_编号 DESC";
  return sql; */
  var sql = "SELECT * FROM tlk_gyfh_ft_01_freighttrial_by_month ";
  var flag = IsRole("系统管理员,总经理,供应链管理部负责人");
  if (flag == false){
    var user = getWebUser(); // 添加：在本函数内获取当前用户
    var flowId = "__eGfOEMOAWHCpZyRRC3v";
    var users = GetAgents(user, flowId);
    sql = UserSqlByUserField(sql, "填写人", users); // 使用 sql，避免未定义的 sourceSql
  }
  sql += " ORDER BY ITEM_编号 DESC";
  return sql;
})()

请使用mcp工具访问数据库，解释一下tlk_psm_01_personal_affairs_management？以下是MySQL的连接配置
"MYSQL_HOST": "************",
          "MYSQL_PORT": "3307",
          "MYSQL_USER": "root",
          "MYSQL_PASSWORD": "Teemlink2010",
          "MYSQL_DATABASE": "magic5"