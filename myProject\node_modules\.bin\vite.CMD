@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ITSoftware\code\node\myProject\node_modules\.pnpm\vite@4.5.2\node_modules\vite\bin\node_modules;D:\ITSoftware\code\node\myProject\node_modules\.pnpm\vite@4.5.2\node_modules\vite\node_modules;D:\ITSoftware\code\node\myProject\node_modules\.pnpm\vite@4.5.2\node_modules;D:\ITSoftware\code\node\myProject\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ITSoftware\code\node\myProject\node_modules\.pnpm\vite@4.5.2\node_modules\vite\bin\node_modules;D:\ITSoftware\code\node\myProject\node_modules\.pnpm\vite@4.5.2\node_modules\vite\node_modules;D:\ITSoftware\code\node\myProject\node_modules\.pnpm\vite@4.5.2\node_modules;D:\ITSoftware\code\node\myProject\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\vite@4.5.2\node_modules\vite\bin\vite.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\vite@4.5.2\node_modules\vite\bin\vite.js" %*
)
